/// شاشة إدارة الجرد الدوري
/// تعرض جلسات الجرد مع إمكانية إنشاء وإدارة جلسات جديدة
library;

import 'package:flutter/material.dart';
import '../models/inventory_count.dart';
import '../models/warehouse.dart';
import '../services/inventory_count_service.dart';
import '../services/warehouse_service.dart';
import '../constants/app_colors.dart';
import '../widgets/loading_widget.dart';
// import 'inventory_count_details_screen.dart';
// import 'add_inventory_count_screen.dart';

class InventoryCountScreen extends StatefulWidget {
  const InventoryCountScreen({super.key});

  @override
  State<InventoryCountScreen> createState() => _InventoryCountScreenState();
}

class _InventoryCountScreenState extends State<InventoryCountScreen>
    with TickerProviderStateMixin {
  final InventoryCountService _countService = InventoryCountService();
  final WarehouseService _warehouseService = WarehouseService();
  final TextEditingController _searchController = TextEditingController();

  List<InventoryCount> _allCounts = [];
  List<InventoryCount> _filteredCounts = [];
  List<Warehouse> _warehouses = [];
  bool _isLoading = true;

  // فلاتر
  InventoryCountStatus? _selectedStatus;
  InventoryCountType? _selectedType;
  int? _selectedWarehouseId;
  DateTimeRange? _selectedDateRange;

  // إحصائيات
  Map<String, int> _statusCounts = {};

  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      final counts = await _countService.getAllInventoryCounts();
      final warehouses = await _warehouseService.getAllWarehouses(
        activeOnly: true,
      );

      setState(() {
        _allCounts = counts;
        _filteredCounts = counts;
        _warehouses = warehouses;
        _isLoading = false;
      });

      _calculateStatusCounts();
      _applyFilters();
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('خطأ في تحميل البيانات: $e');
    }
  }

  void _calculateStatusCounts() {
    _statusCounts = {
      'all': _allCounts.length,
      'draft': _allCounts
          .where((c) => c.status == InventoryCountStatus.draft)
          .length,
      'in_progress': _allCounts
          .where((c) => c.status == InventoryCountStatus.inProgress)
          .length,
      'completed': _allCounts
          .where((c) => c.status == InventoryCountStatus.completed)
          .length,
      'approved': _allCounts
          .where((c) => c.status == InventoryCountStatus.approved)
          .length,
    };
  }

  void _applyFilters() {
    List<InventoryCount> filtered = List.from(_allCounts);

    // فلتر النص
    if (_searchController.text.isNotEmpty) {
      final searchTerm = _searchController.text.toLowerCase();
      filtered = filtered
          .where(
            (count) =>
                count.title.toLowerCase().contains(searchTerm) ||
                count.countNumber.toLowerCase().contains(searchTerm) ||
                (count.description?.toLowerCase().contains(searchTerm) ??
                    false),
          )
          .toList();
    }

    // فلتر الحالة
    if (_selectedStatus != null) {
      filtered = filtered
          .where((count) => count.status == _selectedStatus)
          .toList();
    }

    // فلتر النوع
    if (_selectedType != null) {
      filtered = filtered
          .where((count) => count.countType == _selectedType)
          .toList();
    }

    // فلتر المستودع
    if (_selectedWarehouseId != null) {
      filtered = filtered
          .where((count) => count.warehouseId == _selectedWarehouseId)
          .toList();
    }

    // فلتر التاريخ
    if (_selectedDateRange != null) {
      filtered = filtered
          .where(
            (count) =>
                count.scheduledDate.isAfter(
                  _selectedDateRange!.start.subtract(const Duration(days: 1)),
                ) &&
                count.scheduledDate.isBefore(
                  _selectedDateRange!.end.add(const Duration(days: 1)),
                ),
          )
          .toList();
    }

    setState(() {
      _filteredCounts = filtered;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الجرد'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: [
            Tab(
              text: 'الكل (${_statusCounts['all'] ?? 0})',
              icon: const Icon(Icons.inventory, size: 16),
            ),
            Tab(
              text: 'مسودة (${_statusCounts['draft'] ?? 0})',
              icon: const Icon(Icons.edit_note, size: 16),
            ),
            Tab(
              text: 'قيد التنفيذ (${_statusCounts['in_progress'] ?? 0})',
              icon: const Icon(Icons.play_arrow, size: 16),
            ),
            Tab(
              text: 'مكتملة (${_statusCounts['completed'] ?? 0})',
              icon: const Icon(Icons.check_circle, size: 16),
            ),
            Tab(
              text: 'معتمدة (${_statusCounts['approved'] ?? 0})',
              icon: const Icon(Icons.verified, size: 16),
            ),
          ],
          onTap: (index) {
            switch (index) {
              case 0:
                _selectedStatus = null;
                break;
              case 1:
                _selectedStatus = InventoryCountStatus.draft;
                break;
              case 2:
                _selectedStatus = InventoryCountStatus.inProgress;
                break;
              case 3:
                _selectedStatus = InventoryCountStatus.completed;
                break;
              case 4:
                _selectedStatus = InventoryCountStatus.approved;
                break;
            }
            _applyFilters();
          },
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
            tooltip: 'فلترة',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: _isLoading
          ? const LoadingWidget()
          : Column(
              children: [
                _buildSearchBar(),
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: List.generate(5, (index) => _buildCountsList()),
                  ),
                ),
              ],
            ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showAddCountDialog,
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        icon: const Icon(Icons.add),
        label: const Text('جرد جديد'),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
      ),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'البحث في جلسات الجرد...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    _applyFilters();
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Colors.white,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
        onChanged: (_) => _applyFilters(),
      ),
    );
  }

  Widget _buildCountsList() {
    if (_filteredCounts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inventory_2_outlined, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'لا توجد جلسات جرد',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'ابدأ بإنشاء جلسة جرد جديدة',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredCounts.length,
      itemBuilder: (context, index) {
        final count = _filteredCounts[index];
        return _buildCountCard(count);
      },
    );
  }

  Widget _buildCountCard(InventoryCount count) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _navigateToCountDetails(count),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          count.title,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'رقم الجرد: ${count.countNumber}',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  _buildStatusChip(count.status),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Icon(Icons.category, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    count.typeDisplay,
                    style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                  ),
                  const SizedBox(width: 16),
                  Icon(Icons.schedule, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    _formatDate(count.scheduledDate),
                    style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                  ),
                ],
              ),
              if (count.description?.isNotEmpty == true) ...[
                const SizedBox(height: 8),
                Text(
                  count.description!,
                  style: TextStyle(fontSize: 14, color: Colors.grey[700]),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  if (count.isOverdue)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.red[100],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        'متأخر',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.red[800],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    )
                  else
                    const SizedBox(),
                  Row(
                    children: [
                      if (count.canStart)
                        TextButton.icon(
                          onPressed: () => _startCount(count),
                          icon: const Icon(Icons.play_arrow, size: 16),
                          label: const Text('بدء'),
                          style: TextButton.styleFrom(
                            foregroundColor: AppColors.primary,
                          ),
                        ),
                      const SizedBox(width: 8),
                      TextButton.icon(
                        onPressed: () => _navigateToCountDetails(count),
                        icon: const Icon(Icons.visibility, size: 16),
                        label: const Text('عرض'),
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.grey[700],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(InventoryCountStatus status) {
    Color backgroundColor;
    Color textColor;
    IconData icon;

    switch (status) {
      case InventoryCountStatus.draft:
        backgroundColor = Colors.grey[200]!;
        textColor = Colors.grey[800]!;
        icon = Icons.edit_note;
        break;
      case InventoryCountStatus.inProgress:
        backgroundColor = Colors.blue[100]!;
        textColor = Colors.blue[800]!;
        icon = Icons.play_arrow;
        break;
      case InventoryCountStatus.completed:
        backgroundColor = Colors.orange[100]!;
        textColor = Colors.orange[800]!;
        icon = Icons.check_circle;
        break;
      case InventoryCountStatus.approved:
        backgroundColor = Colors.green[100]!;
        textColor = Colors.green[800]!;
        icon = Icons.verified;
        break;
      case InventoryCountStatus.cancelled:
        backgroundColor = Colors.red[100]!;
        textColor = Colors.red[800]!;
        icon = Icons.cancel;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: textColor),
          const SizedBox(width: 4),
          Text(
            status.displayName,
            style: TextStyle(
              fontSize: 12,
              color: textColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('فلترة جلسات الجرد'),
        content: StatefulBuilder(
          builder: (context, setDialogState) => SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // فلتر النوع
                const Text(
                  'نوع الجرد:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                DropdownButtonFormField<InventoryCountType>(
                  value: _selectedType,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  hint: const Text('اختر نوع الجرد'),
                  items: InventoryCountType.values.map((type) {
                    return DropdownMenuItem(
                      value: type,
                      child: Text(type.displayName),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setDialogState(() => _selectedType = value);
                  },
                ),
                const SizedBox(height: 16),

                // فلتر المستودع
                const Text(
                  'المستودع:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                DropdownButtonFormField<int>(
                  value: _selectedWarehouseId,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  hint: const Text('اختر المستودع'),
                  items: _warehouses.map((warehouse) {
                    return DropdownMenuItem(
                      value: warehouse.id,
                      child: Text(warehouse.name),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setDialogState(() => _selectedWarehouseId = value);
                  },
                ),
                const SizedBox(height: 16),

                // فلتر التاريخ
                const Text(
                  'فترة التاريخ:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                InkWell(
                  onTap: () async {
                    final dateRange = await showDateRangePicker(
                      context: context,
                      firstDate: DateTime(2020),
                      lastDate: DateTime(2030),
                      initialDateRange: _selectedDateRange,
                    );
                    if (dateRange != null) {
                      setDialogState(() => _selectedDateRange = dateRange);
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.date_range),
                        const SizedBox(width: 8),
                        Text(
                          _selectedDateRange != null
                              ? '${_formatDate(_selectedDateRange!.start)} - ${_formatDate(_selectedDateRange!.end)}'
                              : 'اختر فترة التاريخ',
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _selectedType = null;
                _selectedWarehouseId = null;
                _selectedDateRange = null;
              });
              _applyFilters();
              Navigator.pop(context);
            },
            child: const Text('مسح الفلاتر'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              _applyFilters();
              Navigator.pop(context);
            },
            child: const Text('تطبيق'),
          ),
        ],
      ),
    );
  }

  void _showAddCountDialog() {
    // TODO: Implement AddInventoryCountScreen
    _showErrorSnackBar('شاشة إضافة الجرد قيد التطوير');
  }

  void _navigateToCountDetails(InventoryCount count) {
    // TODO: Implement InventoryCountDetailsScreen
    _showErrorSnackBar('شاشة تفاصيل الجرد قيد التطوير');
  }

  Future<void> _startCount(InventoryCount count) async {
    try {
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('بدء الجرد'),
          content: Text('هل تريد بدء جلسة الجرد "${count.title}"؟'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.pop(context, true),
              child: const Text('بدء'),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        await _countService.startInventoryCount(count.id!, 'المستخدم الحالي');
        _showSuccessSnackBar('تم بدء جلسة الجرد بنجاح');
        _loadData();
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في بدء الجرد: $e');
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
